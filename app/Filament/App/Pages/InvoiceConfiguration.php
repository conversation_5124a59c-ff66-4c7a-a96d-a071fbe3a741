<?php

namespace App\Filament\App\Pages;

use Filament\Actions\Action;
use Filament\Forms\Components\Card;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\ViewField;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Illuminate\Support\Facades\File;

class InvoiceConfiguration extends Page
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    protected static ?string $navigationLabel = 'Konfiguracja faktur';
    protected static ?string $slug = 'invoice-configuration';
    protected static string $view = 'filament.app.pages.invoice-configuration';
    
    protected ?string $heading = 'Konfiguracja szablonów faktur';
    
    public ?array $data = [];
    public array $availableTemplates = [];
    public array $availableFields = [];
    public ?string $selectedTemplate = null;

    public static function getNavigationGroup(): ?string
    {
        return __('app._.settings');
    }

    public static function shouldRegisterNavigation(): bool
    {
        return auth()->user()->isTenantAdmin();
    }

    public function mount(): void
    {
        $this->loadTemplateData();
        $this->form->fill([
            'selected_template' => null,
        ]);
    }

    protected function loadTemplateData(): void
    {
        $templatePath = resource_path('views/print/trade_docs/template.json');
        
        if (File::exists($templatePath)) {
            $templateData = json_decode(File::get($templatePath), true);
            $this->availableTemplates = $templateData['templates'] ?? [];
            $this->availableFields = $templateData['available_fields'] ?? [];
        } else {
            $this->availableTemplates = [];
            $this->availableFields = [];
        }
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Wybór szablonu faktury')
                    ->description('Wybierz szablon, który będzie używany do generowania faktur')
                    ->schema([
                        Select::make('selected_template')
                            ->label('Szablon faktury')
                            ->options($this->getTemplateOptions())
                            ->placeholder('Wybierz szablon...')
                            ->reactive()
                            ->afterStateUpdated(function ($state) {
                                $this->selectedTemplate = $state;
                                $this->dispatch('template-changed', $state);
                            })
                            ->helperText('Dostępne szablony faktur w systemie'),
                    ])
                    ->collapsible()
                    ->persistCollapsed(),

                Section::make('Podgląd wybranego szablonu')
                    ->description('Informacje o wybranym szablonie')
                    ->schema([
                        ViewField::make('template_preview')
                            ->view('filament.app.components.template-preview')
                            ->viewData([
                                'template' => $this->getSelectedTemplateData(),
                            ])
                            ->visible(fn () => !empty($this->selectedTemplate)),
                    ])
                    ->visible(fn () => !empty($this->selectedTemplate))
                    ->collapsible()
                    ->persistCollapsed(),

                Section::make('Dostępne pola faktury')
                    ->description('Lista pól, które mogą być umieszczone na fakturze')
                    ->schema([
                        ViewField::make('available_fields')
                            ->view('filament.app.components.available-fields')
                            ->viewData([
                                'fields' => $this->availableFields,
                                'selectedTemplate' => $this->getSelectedTemplateData(),
                            ]),
                    ])
                    ->collapsible()
                    ->persistCollapsed(),
            ])
            ->statePath('data');
    }

    protected function getTemplateOptions(): array
    {
        $options = [];
        foreach ($this->availableTemplates as $template) {
            $options[$template['id']] = $template['template_name'] . ' (v' . $template['version'] . ')';
        }
        return $options;
    }

    protected function getSelectedTemplateData(): ?array
    {
        if (empty($this->selectedTemplate)) {
            return null;
        }

        foreach ($this->availableTemplates as $template) {
            if ($template['id'] === $this->selectedTemplate) {
                return $template;
            }
        }

        return null;
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('save_configuration')
                ->label('Zapisz konfigurację')
                ->icon('heroicon-o-check')
                ->color('success')
                ->action(function () {
                    $this->saveConfiguration();
                })
                ->disabled(fn () => empty($this->selectedTemplate)),

            Action::make('refresh_templates')
                ->label('Odśwież szablony')
                ->icon('heroicon-o-arrow-path')
                ->color('gray')
                ->action(function () {
                    $this->loadTemplateData();
                    Notification::make()
                        ->title('Szablony zostały odświeżone')
                        ->success()
                        ->send();
                }),
        ];
    }

    protected function saveConfiguration(): void
    {
        // Here you would typically save the configuration to database
        // For now, we'll just show a notification
        
        Notification::make()
            ->title('Konfiguracja została zapisana')
            ->body('Wybrany szablon: ' . $this->getSelectedTemplateData()['template_name'] ?? 'Nieznany')
            ->success()
            ->send();
    }
}
