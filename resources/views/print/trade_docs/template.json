{"templates": [{"id": "v2_fvs", "template_name": "Faktura VAT Sprzedaży v2", "version": "2.0", "description": "Template for VAT sales invoices", "template_file": "v2/fvs.blade.php", "type": "sales_invoice", "supported_fields": ["issuer_address", "seller_address", "buyer_address", "bank_data", "vat_data", "document_number", "dates", "items", "totals", "payment_info"]}, {"id": "v2_fvk", "template_name": "Faktura VAT Korekta v2", "version": "2.0", "description": "Template for VAT correction invoices", "template_file": "v2/fvk.blade.php", "type": "correction_invoice", "supported_fields": ["issuer_address", "seller_address", "buyer_address", "bank_data", "vat_data", "document_number", "dates", "items", "totals"]}], "available_fields": [{"field_name": "issuer_address", "display_name": "<PERSON><PERSON> w<PERSON>", "description": "Company issuing the invoice address details"}, {"field_name": "seller_address", "display_name": "<PERSON><PERSON> s<PERSON>rz<PERSON>", "description": "Seller address details (if different from issuer)"}, {"field_name": "buyer_address", "display_name": "<PERSON><PERSON>", "description": "Buyer/customer address details"}, {"field_name": "bank_data", "display_name": "Dane bankowe", "description": "Bank account information"}, {"field_name": "vat_data", "display_name": "Dane VAT", "description": "VAT calculations and rates"}, {"field_name": "document_number", "display_name": "Numer dokumentu", "description": "Invoice number and series"}, {"field_name": "dates", "display_name": "<PERSON><PERSON>", "description": "Issue date, sale date, payment due date"}, {"field_name": "items", "display_name": "<PERSON><PERSON><PERSON><PERSON> fak<PERSON>", "description": "Invoice line items with quantities and prices"}, {"field_name": "totals", "display_name": "<PERSON><PERSON>", "description": "Total amounts, VAT totals, grand total"}, {"field_name": "payment_info", "display_name": "Informacje o płatności", "description": "Payment method, status, and related information"}]}